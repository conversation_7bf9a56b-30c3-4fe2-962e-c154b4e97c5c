import React, { useState, useEffect } from 'react';
import Header from '../components/Header';
import ModelSelector from '../components/ModelSelector';
import TextInput from '../components/TextInput';
import WaveSurferPlayer from '../components/WaveSurferPlayer';
import AudioSettings from '../components/AudioSettings';
import SampleTexts from '../components/SampleTexts';
import apiService from '../services/apiService';

const TTSPage = () => {
  const [apiStatus, setApiStatus] = useState('checking');
  const [models, setModels] = useState([]);
  const [currentModel, setCurrentModel] = useState(null);
  const [speakers, setSpeakers] = useState([]);
  const [voiceInfo, setVoiceInfo] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [audioUrl, setAudioUrl] = useState(null);

  // Cleanup blob URLs when component unmounts or audioUrl changes
  useEffect(() => {
    return () => {
      if (audioUrl && audioUrl.startsWith('blob:')) {
        URL.revokeObjectURL(audioUrl);
      }
    };
  }, [audioUrl]);

  // Cleanup on component unmount
  useEffect(() => {
    return () => {
      if (audioUrl && audioUrl.startsWith('blob:')) {
        URL.revokeObjectURL(audioUrl);
      }
    };
  }, []);
  
  // TTS Settings
  const [text, setText] = useState('');
  const [speakerId, setSpeakerId] = useState(0);
  const [speechRate, setSpeechRate] = useState(1.0);
  const [noiseScale, setNoiseScale] = useState(0.667);
  const [noiseW, setNoiseW] = useState(0.8);
  const [sentenceSilence, setSentenceSilence] = useState(0.2);

  // Voice cloning states
  const [useVoiceCloning, setUseVoiceCloning] = useState(false);
  const [referenceAudio, setReferenceAudio] = useState(null);
  const [cloningModel, setCloningModel] = useState('f5_tts');
  const [cloningStrength, setCloningStrength] = useState(1.0);
  const [voiceCloningAvailable, setVoiceCloningAvailable] = useState(false);
  const [availableCloningModels, setAvailableCloningModels] = useState([]);

  // Timbre transfer states
  const [useTimbreTransfer, setUseTimbreTransfer] = useState(false);
  const [timbreTransferAvailable, setTimbreTransferAvailable] = useState(false);
  const [timbreStrength, setTimbreStrength] = useState(1.0);
  const [preserveProsody, setPreserveProsody] = useState(true);

  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    try {
      const status = await apiService.checkApiStatus();
      setApiStatus(status);
      
      if (status === 'ready') {
        const [modelsData, voiceData, speakersData, featuresData] = await Promise.all([
          apiService.loadAvailableModels(),
          apiService.loadVoiceInfo(),
          apiService.loadSpeakers(),
          apiService.getAvailableFeatures()
        ]);

        setModels(modelsData);
        setVoiceInfo(voiceData);
        setSpeakers(speakersData);

        // Set voice cloning availability
        setVoiceCloningAvailable(featuresData.voice_cloning.enabled);
        setAvailableCloningModels(featuresData.voice_cloning.available_models);

        // Set timbre transfer availability
        setTimbreTransferAvailable(featuresData.timbre_transfer.enabled);

        // Set default cloning model if available
        if (featuresData.voice_cloning.available_models.length > 0) {
          setCloningModel(featuresData.voice_cloning.available_models[0]);
        }

        if (modelsData.length > 0) {
          setCurrentModel(modelsData[0]);
        }
      }
    } catch (error) {
      console.error('Failed to initialize app:', error);
      setApiStatus('error');
    }
  };

  const handleModelSwitch = async (model) => {
    try {
      setIsLoading(true);
      await apiService.switchModel(model.id);
      setCurrentModel(model);
      
      // Reload speakers for new model
      const speakersData = await apiService.loadSpeakers();
      setSpeakers(speakersData);
      setSpeakerId(0);
    } catch (error) {
      console.error('Failed to switch model:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSynthesize = async () => {
    if (!text.trim()) return;

    // Check if voice cloning is enabled but not available
    if (useVoiceCloning && !voiceCloningAvailable) {
      alert('Voice cloning is not available. Please install the required packages.');
      return;
    }

    // Check if timbre transfer is enabled but not available
    if (useTimbreTransfer && !timbreTransferAvailable) {
      alert('Timbre transfer is not available. Please install the required packages.');
      return;
    }

    // Check if reference audio is needed but not provided
    if ((useVoiceCloning || useTimbreTransfer) && !referenceAudio) {
      alert('Please select a reference audio file for voice cloning or timbre transfer.');
      return;
    }

    try {
      setIsLoading(true);

      // Convert reference audio to base64 if needed
      let referenceAudioBase64 = null;
      if ((useVoiceCloning || useTimbreTransfer) && referenceAudio) {
        referenceAudioBase64 = await new Promise((resolve) => {
          const reader = new FileReader();
          reader.onload = () => {
            const base64 = reader.result.split(',')[1]; // Remove data:audio/... prefix
            resolve(base64);
          };
          reader.readAsDataURL(referenceAudio);
        });
      }

      // Clean up previous blob URL
      if (audioUrl && audioUrl.startsWith('blob:')) {
        URL.revokeObjectURL(audioUrl);
      }

      const newAudioUrl = await apiService.generateAudio({
        text,
        speaker_id: speakerId,
        speech_rate: speechRate,
        noise_scale: noiseScale,
        noise_w: noiseW,
        sentence_silence: sentenceSilence,
        use_voice_cloning: useVoiceCloning,
        use_timbre_transfer: useTimbreTransfer,
        reference_audio_base64: referenceAudioBase64,
        cloning_model: cloningModel,
        cloning_strength: cloningStrength,
        timbre_strength: timbreStrength,
        preserve_prosody: preserveProsody,
        language: 'ne'
      });
      setAudioUrl(newAudioUrl);
    } catch (error) {
      console.error('Audio generation failed:', error);
      alert('Audio generation failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSampleTextSelect = (sampleText) => {
    setText(sampleText);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 relative overflow-hidden">
      {/* Keep Same Animated Background */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="particles-container">
          {[...Array(80)].map((_, i) => (
            <div
              key={i}
              className="particle"
              style={{
                animationDelay: `${Math.random() * 20}s`,
                top: `${20 + Math.random() * 60}%`,
                animationDuration: `${15 + Math.random() * 10}s`
              }}
            />
          ))}
        </div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 min-h-screen">
        <Header apiStatus={apiStatus} />

        <div className="container mx-auto px-4 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-6 max-w-full mx-auto">
            {/* Left Column - Model Selection */}
            <div className="lg:col-span-2">
              <ModelSelector
                models={models}
                currentModel={currentModel}
                onModelSwitch={handleModelSwitch}
                isLoading={isLoading}
                voiceInfo={voiceInfo}
              />
            </div>

            {/* Middle Column - Text Input & Audio Output */}
            <div className="lg:col-span-5 space-y-6">
              <TextInput
                text={text}
                setText={setText}
                onSynthesize={handleSynthesize}
                isLoading={isLoading}
                disabled={apiStatus !== 'ready'}
              />

              {audioUrl && (
                <WaveSurferPlayer audioUrl={audioUrl} />
              )}
            </div>

            {/* Right Column - Settings & Sample Texts Side by Side */}
            <div className="lg:col-span-5">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 h-full">
                {/* <div className="w-full">
                  <AudioSettings
                    speakers={speakers}
                    speakerId={speakerId}
                    setSpeakerId={setSpeakerId}
                    speechRate={speechRate}
                    setSpeechRate={setSpeechRate}
                    noiseScale={noiseScale}
                    setNoiseScale={setNoiseScale}
                    noiseW={noiseW}
                    setNoiseW={setNoiseW}
                    sentenceSilence={sentenceSilence}
                    setSentenceSilence={setSentenceSilence}
                    useVoiceCloning={useVoiceCloning}
                    setUseVoiceCloning={setUseVoiceCloning}
                    referenceAudio={referenceAudio}
                    setReferenceAudio={setReferenceAudio}
                    cloningModel={cloningModel}
                    setCloningModel={setCloningModel}
                    cloningStrength={cloningStrength}
                    setCloningStrength={setCloningStrength}
                    voiceCloningAvailable={voiceCloningAvailable}
                    availableCloningModels={availableCloningModels}
                    useTimbreTransfer={useTimbreTransfer}
                    setUseTimbreTransfer={setUseTimbreTransfer}
                    timbreTransferAvailable={timbreTransferAvailable}
                    timbreStrength={timbreStrength}
                    setTimbreStrength={setTimbreStrength}
                    preserveProsody={preserveProsody}
                    setPreserveProsody={setPreserveProsody}
                  />
                </div> */}

                <div className="w-full">
                  <SampleTexts onSelectText={handleSampleTextSelect} />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TTSPage;
