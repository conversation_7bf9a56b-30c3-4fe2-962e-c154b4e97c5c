#!/bin/bash

# Voice Cloning Models Installation Script
# This script installs various voice cloning models for the Piper TTS API

echo "🎤 Installing Voice Cloning Models for Piper TTS API"
echo "=================================================="

# Check if virtual environment is activated
if [[ "$VIRTUAL_ENV" == "" ]]; then
    echo "⚠️  Warning: No virtual environment detected."
    echo "   It's recommended to activate your virtual environment first:"
    echo "   source .venv/bin/activate"
    echo ""
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

echo "📦 Installing voice cloning dependencies..."

# Function to install a package with error handling
install_package() {
    local package=$1
    local description=$2
    
    echo "Installing $description..."
    if pip install "$package"; then
        echo "✅ $description installed successfully"
    else
        echo "❌ Failed to install $description"
        echo "   You can try installing manually: pip install $package"
    fi
    echo ""
}

# Core dependencies
echo "🔧 Installing core dependencies..."
install_package "torch torchvision torchaudio" "PyTorch (CPU version)"
install_package "soundfile" "SoundFile for audio processing"
install_package "librosa" "Librosa for audio analysis"
install_package "numpy scipy" "NumPy and SciPy"

# F5-TTS
echo "🚀 Installing F5-TTS..."
install_package "f5-tts" "F5-TTS (Fast and High Quality)"

# XTTS v2 (Coqui TTS)
echo "🎯 Installing XTTS v2..."
install_package "TTS" "Coqui TTS (XTTS v2)"

# Bark
echo "🐕 Installing Bark..."
install_package "git+https://github.com/suno-ai/bark.git" "Bark (Expressive Voice Cloning)"

# Tortoise TTS
echo "🐢 Installing Tortoise TTS..."
install_package "tortoise-tts" "Tortoise TTS (Ultra High Quality)"

# Additional audio processing libraries
echo "🎵 Installing additional audio libraries..."
install_package "pydub" "PyDub for audio manipulation"
install_package "webrtcvad" "WebRTC VAD for voice activity detection"
install_package "resemblyzer" "Resemblyzer for speaker verification"

# Optional: Install CUDA version of PyTorch if NVIDIA GPU is available
if command -v nvidia-smi &> /dev/null; then
    echo "🎮 NVIDIA GPU detected!"
    read -p "Install CUDA version of PyTorch for GPU acceleration? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "Installing PyTorch with CUDA support..."
        pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
        echo "✅ PyTorch with CUDA installed"
    fi
fi

echo ""
echo "🎉 Voice cloning models installation completed!"
echo ""
echo "📋 Summary of installed models:"
echo "   • F5-TTS: Fast and high-quality voice cloning"
echo "   • XTTS v2: Multilingual voice cloning by Coqui"
echo "   • Bark: Expressive voice cloning with emotions"
echo "   • Tortoise TTS: Ultra high-quality voice cloning"
echo "   • SeedVC: Advanced timbre transfer (placeholder)"
echo "   • OpenVoice v2: Instant voice cloning (placeholder)"
echo ""
echo "🔧 To test the installation, restart your Piper API server:"
echo "   cd piper_api && python fastapi_server.py"
echo ""
echo "🌐 Then check available models at:"
echo "   http://localhost:8000/features"
echo ""
echo "⚠️  Note: Some models may require additional setup or model downloads"
echo "   on first use. Check the logs for any additional requirements."
