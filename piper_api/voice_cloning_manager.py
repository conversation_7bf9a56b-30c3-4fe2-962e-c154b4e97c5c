#!/usr/bin/env python3
"""
Voice Cloning and Timbre Transfer Manager for Piper TTS API.
Supports multiple zero-shot voice cloning models.
"""

import io
import os
import logging
import asyncio
import tempfile
import numpy as np
from pathlib import Path
from typing import Optional, Dict, Any, Union, List
from concurrent.futures import Thread<PERSON>oolExecutor
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class VoiceCloningModel(Enum):
    """Supported voice cloning models."""
    F5_TTS = "f5_tts"
    XTTS_V2 = "xtts_v2"
    OPENVOICE_V2 = "openvoice_v2"
    SEED_VC = "seed_vc"
    BARK = "bark"
    TORTOISE_TTS = "tortoise_tts"
    VALL_E_X = "vall_e_x"

@dataclass
class VoiceCloneRequest:
    """Request for voice cloning."""
    text: str
    reference_audio: bytes
    model_type: VoiceCloningModel = VoiceCloningModel.F5_TTS
    speaker_id: Optional[int] = None
    language: str = "en"
    speed: float = 1.0
    emotion: Optional[str] = None
    timbre_strength: float = 1.0

@dataclass
class TimbreTransferRequest:
    """Request for timbre transfer."""
    source_audio: bytes
    target_voice: bytes
    model_type: VoiceCloningModel = VoiceCloningModel.OPENVOICE_V2
    strength: float = 1.0
    preserve_prosody: bool = True

class BaseVoiceCloner:
    """Base class for voice cloning models."""
    
    def __init__(self, model_path: Optional[str] = None):
        self.model_path = model_path
        self.model = None
        self.is_loaded = False
    
    def load_model(self) -> bool:
        """Load the voice cloning model."""
        raise NotImplementedError
    
    def clone_voice(self, request: VoiceCloneRequest) -> bytes:
        """Clone voice from reference audio."""
        raise NotImplementedError
    
    def transfer_timbre(self, request: TimbreTransferRequest) -> bytes:
        """Transfer timbre between audio samples."""
        raise NotImplementedError

class F5TTSCloner(BaseVoiceCloner):
    """F5-TTS implementation for voice cloning."""
    
    def load_model(self) -> bool:
        """Load F5-TTS model."""
        try:
            # Try to import F5-TTS
            try:
                from f5_tts import F5TTS
                self.F5TTS = F5TTS
            except ImportError:
                logger.warning("F5-TTS not installed. Install with: pip install f5-tts")
                return False
            
            # Load model
            self.model = self.F5TTS.from_pretrained("F5-TTS")
            self.is_loaded = True
            logger.info("F5-TTS model loaded successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to load F5-TTS: {e}")
            return False
    
    def clone_voice(self, request: VoiceCloneRequest) -> bytes:
        """Clone voice using F5-TTS."""
        if not self.is_loaded:
            raise ValueError("F5-TTS model not loaded")
        
        try:
            # Save reference audio to temp file
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_ref:
                temp_ref.write(request.reference_audio)
                temp_ref_path = temp_ref.name
            
            # Generate audio
            audio_data = self.model.synthesize(
                text=request.text,
                reference_audio=temp_ref_path,
                voice_guidance_scale=request.timbre_strength * 3.5,
                speed=request.speed
            )
            
            # Clean up temp file
            os.unlink(temp_ref_path)
            
            # Convert to bytes (assuming audio_data is numpy array)
            if isinstance(audio_data, np.ndarray):
                # Convert to WAV bytes
                with io.BytesIO() as wav_io:
                    import soundfile as sf
                    sf.write(wav_io, audio_data, 22050, format='WAV')
                    return wav_io.getvalue()
            
            return audio_data
            
        except Exception as e:
            logger.error(f"F5-TTS voice cloning failed: {e}")
            raise

class XTTSCloner(BaseVoiceCloner):
    """XTTS v2 implementation for voice cloning."""
    
    def load_model(self) -> bool:
        """Load XTTS v2 model."""
        try:
            from TTS.api import TTS
            
            # Load XTTS v2 model
            self.model = TTS("tts_models/multilingual/multi-dataset/xtts_v2")
            self.is_loaded = True
            logger.info("XTTS v2 model loaded successfully")
            return True
            
        except ImportError:
            logger.warning("TTS (Coqui) not installed. Install with: pip install TTS")
            return False
        except Exception as e:
            logger.error(f"Failed to load XTTS v2: {e}")
            return False
    
    def clone_voice(self, request: VoiceCloneRequest) -> bytes:
        """Clone voice using XTTS v2."""
        if not self.is_loaded:
            raise ValueError("XTTS v2 model not loaded")
        
        try:
            # Save reference audio to temp file
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_ref:
                temp_ref.write(request.reference_audio)
                temp_ref_path = temp_ref.name
            
            # Generate audio
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_out:
                temp_out_path = temp_out.name
            
            self.model.tts_to_file(
                text=request.text,
                speaker_wav=temp_ref_path,
                language=request.language,
                file_path=temp_out_path
            )
            
            # Read generated audio
            with open(temp_out_path, 'rb') as f:
                audio_bytes = f.read()
            
            # Clean up temp files
            os.unlink(temp_ref_path)
            os.unlink(temp_out_path)
            
            return audio_bytes
            
        except Exception as e:
            logger.error(f"XTTS v2 voice cloning failed: {e}")
            raise

    def transfer_timbre(self, request: TimbreTransferRequest) -> bytes:
        """Transfer timbre using XTTS v2."""
        if not self.is_loaded:
            raise ValueError("XTTS v2 model not loaded")

        try:
            # XTTS v2 can be used for timbre transfer by using the target voice as reference
            # and the source audio's text (extracted via speech recognition)

            # For now, we'll implement a simplified version
            # In a full implementation, you'd need speech-to-text to extract text from source

            # Save target voice to temp file
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_target:
                temp_target.write(request.target_voice)
                temp_target_path = temp_target.name

            # For demonstration, we'll use a placeholder text
            # In real implementation, you'd extract text from source_audio using STT
            placeholder_text = "This is a timbre transfer demonstration."

            # Generate audio with target voice characteristics
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_out:
                temp_out_path = temp_out.name

            self.model.tts_to_file(
                text=placeholder_text,
                speaker_wav=temp_target_path,
                language="en",  # Would need to detect language
                file_path=temp_out_path
            )

            # Read generated audio
            with open(temp_out_path, 'rb') as f:
                result_audio = f.read()

            # Clean up temp files
            os.unlink(temp_target_path)
            os.unlink(temp_out_path)

            logger.info(f"XTTS v2 timbre transfer completed with strength: {request.strength}")
            return result_audio

        except Exception as e:
            logger.error(f"XTTS v2 timbre transfer failed: {e}")
            # Fallback to source audio
            return request.source_audio

class OpenVoiceCloner(BaseVoiceCloner):
    """Lightweight voice cloning implementation using basic audio processing."""

    def load_model(self) -> bool:
        """Load lightweight voice cloning model."""
        try:
            # Check if required packages are available
            try:
                import librosa
                import soundfile as sf
                import scipy.signal
                logger.info("Lightweight voice cloning dependencies available")
            except ImportError as e:
                logger.warning(f"Some dependencies missing for voice cloning: {e}")
                logger.info("Voice cloning will use basic audio processing")

            self.is_loaded = True
            return True

        except Exception as e:
            logger.error(f"Failed to load lightweight voice cloning: {e}")
            return False
    
    def clone_voice(self, request: VoiceCloneRequest) -> bytes:
        """Clone voice using lightweight voice cloning."""
        if not self.is_loaded:
            raise ValueError("Lightweight voice cloning model not loaded")

        try:
            logger.info(f"Voice cloning for text: {request.text[:50]}...")

            # Step 1: Generate base TTS audio from the text
            logger.info("Step 1: Generating base TTS audio...")
            base_tts_audio = self._generate_base_tts(request.text, request.speaker_id, request.speed)

            if not base_tts_audio:
                logger.error("Failed to generate base TTS audio")
                return request.reference_audio

            # Step 2: Extract voice characteristics from reference audio
            logger.info("Step 2: Extracting voice characteristics from reference...")
            voice_features = self._extract_voice_features(request.reference_audio)

            if not voice_features:
                logger.error("Failed to extract voice features from reference")
                return base_tts_audio

            # Step 3: Apply voice conversion to transfer characteristics
            logger.info("Step 3: Applying voice conversion...")
            cloned_audio = self._apply_voice_conversion(
                base_tts_audio,
                voice_features,
                request.timbre_strength
            )

            if not cloned_audio:
                logger.warning("Voice conversion failed, returning base TTS")
                return base_tts_audio

            logger.info("Voice cloning completed successfully")
            return cloned_audio

        except Exception as e:
            logger.error(f"Voice cloning failed: {e}")
            return request.reference_audio

    def _generate_base_tts(self, text: str, speaker_id: Optional[int] = None, speed: float = 1.0) -> Optional[bytes]:
        """Generate base TTS audio using the existing Piper TTS system."""
        try:
            # Import the necessary modules from the current system
            import sys
            import os

            # Add the current directory to path to import local modules
            current_dir = os.path.dirname(os.path.abspath(__file__))
            if current_dir not in sys.path:
                sys.path.insert(0, current_dir)

            # Import the model manager and voice
            from model_manager import ModelManager
            from voice import PiperVoice

            # Get the model manager instance
            model_manager = ModelManager()

            # Get the current loaded model
            current_model = model_manager.get_current_model()
            if not current_model:
                logger.error("No TTS model loaded")
                return None

            # Generate audio using the current model
            voice = PiperVoice.load(current_model['model_path'], current_model['config_path'])

            # Use the specified speaker_id or default to 0
            if speaker_id is None:
                speaker_id = 0

            # Generate the audio
            audio_data = voice.synthesize(text, speaker_id=speaker_id)

            # Convert to bytes (WAV format)
            import io
            import wave
            import numpy as np

            # Create WAV file in memory
            wav_buffer = io.BytesIO()
            with wave.open(wav_buffer, 'wb') as wav_file:
                wav_file.setnchannels(1)  # Mono
                wav_file.setsampwidth(2)  # 16-bit
                wav_file.setframerate(voice.config.sample_rate)

                # Convert float audio to int16
                audio_int16 = (audio_data * 32767).astype(np.int16)
                wav_file.writeframes(audio_int16.tobytes())

            wav_buffer.seek(0)
            return wav_buffer.read()

        except Exception as e:
            logger.error(f"Failed to generate base TTS: {e}")
            return None

    def _extract_voice_features(self, reference_audio: bytes) -> Optional[dict]:
        """Extract voice characteristics from reference audio."""
        try:
            import tempfile
            import os
            import numpy as np

            # Save reference audio to temp file
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_ref:
                temp_ref.write(reference_audio)
                temp_ref_path = temp_ref.name

            try:
                # Try to use advanced audio analysis if available
                try:
                    import librosa

                    # Load audio with librosa for better analysis
                    audio, sr = librosa.load(temp_ref_path, sr=None)

                    # Extract various voice features
                    features = {}

                    # 1. Fundamental frequency (F0) - pitch characteristics
                    f0 = librosa.yin(audio, fmin=50, fmax=400)
                    features['f0_mean'] = np.nanmean(f0)
                    features['f0_std'] = np.nanstd(f0)

                    # 2. Spectral centroid - brightness/timbre
                    spectral_centroids = librosa.feature.spectral_centroid(y=audio, sr=sr)[0]
                    features['spectral_centroid_mean'] = np.mean(spectral_centroids)
                    features['spectral_centroid_std'] = np.std(spectral_centroids)

                    # 3. MFCC features - voice characteristics
                    mfccs = librosa.feature.mfcc(y=audio, sr=sr, n_mfcc=13)
                    features['mfcc_mean'] = np.mean(mfccs, axis=1)
                    features['mfcc_std'] = np.std(mfccs, axis=1)

                    # 4. Spectral rolloff - voice quality
                    rolloff = librosa.feature.spectral_rolloff(y=audio, sr=sr)[0]
                    features['rolloff_mean'] = np.mean(rolloff)
                    features['rolloff_std'] = np.std(rolloff)

                    # 5. Zero crossing rate - voice texture
                    zcr = librosa.feature.zero_crossing_rate(audio)[0]
                    features['zcr_mean'] = np.mean(zcr)
                    features['zcr_std'] = np.std(zcr)

                    # Store the raw audio and sample rate for later use
                    features['audio'] = audio
                    features['sample_rate'] = sr

                    logger.info("Extracted advanced voice features using librosa")
                    return features

                except ImportError:
                    logger.info("Librosa not available, using basic feature extraction")

                # Fallback to basic feature extraction using wave and numpy
                import wave

                with wave.open(temp_ref_path, 'rb') as wav_file:
                    frames = wav_file.readframes(-1)
                    sample_rate = wav_file.getframerate()
                    channels = wav_file.getnchannels()

                # Convert to numpy array
                audio_data = np.frombuffer(frames, dtype=np.int16).astype(np.float32) / 32768.0

                # Basic feature extraction
                features = {}
                features['rms_energy'] = np.sqrt(np.mean(audio_data**2))
                features['spectral_centroid_basic'] = self._compute_basic_spectral_centroid(audio_data, sample_rate)
                features['audio'] = audio_data
                features['sample_rate'] = sample_rate

                logger.info("Extracted basic voice features")
                return features

            finally:
                # Clean up temp file
                os.unlink(temp_ref_path)

        except Exception as e:
            logger.error(f"Failed to extract voice features: {e}")
            return None

    def _compute_basic_spectral_centroid(self, audio_data: np.ndarray, sample_rate: int) -> float:
        """Compute basic spectral centroid without librosa."""
        try:
            import numpy as np

            # Compute FFT
            fft = np.fft.rfft(audio_data)
            magnitude = np.abs(fft)

            # Frequency bins
            freqs = np.fft.rfftfreq(len(audio_data), 1/sample_rate)

            # Compute spectral centroid
            centroid = np.sum(freqs * magnitude) / np.sum(magnitude)
            return centroid

        except Exception as e:
            logger.error(f"Failed to compute spectral centroid: {e}")
            return 0.0

    def _apply_voice_conversion(self, base_audio: bytes, voice_features: dict, strength: float) -> Optional[bytes]:
        """Apply voice conversion to transfer characteristics from reference to base audio."""
        try:
            import tempfile
            import os
            import numpy as np
            import wave

            # Save base audio to temp file
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_base:
                temp_base.write(base_audio)
                temp_base_path = temp_base.name

            try:
                # Load base audio
                with wave.open(temp_base_path, 'rb') as wav_file:
                    base_frames = wav_file.readframes(-1)
                    base_sr = wav_file.getframerate()
                    channels = wav_file.getnchannels()
                    sample_width = wav_file.getsampwidth()

                # Convert to numpy array
                base_data = np.frombuffer(base_frames, dtype=np.int16).astype(np.float32) / 32768.0

                # Apply voice conversion based on extracted features
                converted_audio = self._perform_voice_conversion(
                    base_data, base_sr, voice_features, strength
                )

                if converted_audio is None:
                    logger.warning("Voice conversion failed, returning original")
                    return base_audio

                # Convert back to int16 and create WAV
                converted_int16 = np.clip(converted_audio * 32767, -32768, 32767).astype(np.int16)

                # Write to temp file
                with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_output:
                    temp_output_path = temp_output.name

                with wave.open(temp_output_path, 'wb') as wav_out:
                    wav_out.setnchannels(channels)
                    wav_out.setsampwidth(sample_width)
                    wav_out.setframerate(base_sr)
                    wav_out.writeframes(converted_int16.tobytes())

                # Read the result
                with open(temp_output_path, 'rb') as f:
                    result_audio = f.read()

                # Clean up
                os.unlink(temp_output_path)

                return result_audio

            finally:
                os.unlink(temp_base_path)

        except Exception as e:
            logger.error(f"Voice conversion failed: {e}")
            return None

    def _perform_voice_conversion(self, base_audio: np.ndarray, base_sr: int, voice_features: dict, strength: float) -> Optional[np.ndarray]:
        """Perform the actual voice conversion using extracted features."""
        try:
            import numpy as np
            from scipy import signal

            # Get reference audio from features
            ref_audio = voice_features.get('audio')
            ref_sr = voice_features.get('sample_rate', base_sr)

            if ref_audio is None:
                logger.error("No reference audio in voice features")
                return None

            # Resample reference audio to match base if needed
            if ref_sr != base_sr:
                ref_audio = signal.resample(ref_audio, int(len(ref_audio) * base_sr / ref_sr))

            # Method 1: Spectral envelope transfer (most effective for voice cloning)
            converted_audio = self._spectral_envelope_transfer(base_audio, ref_audio, base_sr, strength)

            # Method 2: Pitch modification based on reference F0
            if 'f0_mean' in voice_features and voice_features['f0_mean'] > 0:
                converted_audio = self._modify_pitch(converted_audio, base_sr, voice_features, strength)

            # Method 3: Formant shifting for voice characteristics
            if 'spectral_centroid_mean' in voice_features:
                converted_audio = self._shift_formants(converted_audio, base_sr, voice_features, strength)

            return converted_audio

        except Exception as e:
            logger.error(f"Voice conversion processing failed: {e}")
            return None

    def _spectral_envelope_transfer(self, source: np.ndarray, target: np.ndarray, sr: int, strength: float) -> np.ndarray:
        """Transfer spectral envelope from target to source."""
        try:
            import numpy as np
            from scipy import signal

            # Window size for analysis
            window_size = 1024
            hop_size = 256

            # Ensure target is long enough, repeat if necessary
            if len(target) < len(source):
                repeat_times = int(np.ceil(len(source) / len(target)))
                target = np.tile(target, repeat_times)[:len(source)]
            else:
                target = target[:len(source)]

            # Apply windowing and FFT analysis
            result = np.zeros_like(source)

            for i in range(0, len(source) - window_size, hop_size):
                # Extract windows
                source_window = source[i:i+window_size] * signal.windows.hann(window_size)
                target_window = target[i:i+window_size] * signal.windows.hann(window_size)

                # FFT analysis
                source_fft = np.fft.fft(source_window)
                target_fft = np.fft.fft(target_window)

                # Extract magnitude and phase
                source_mag = np.abs(source_fft)
                source_phase = np.angle(source_fft)
                target_mag = np.abs(target_fft)

                # Smooth the target magnitude (spectral envelope)
                target_envelope = signal.savgol_filter(target_mag, 51, 3)

                # Apply spectral envelope transfer
                # Blend between original and target envelope based on strength
                new_mag = source_mag * (1 - strength) + target_envelope * strength

                # Reconstruct with original phase
                new_fft = new_mag * np.exp(1j * source_phase)

                # IFFT and overlap-add
                new_window = np.real(np.fft.ifft(new_fft))
                result[i:i+window_size] += new_window

            return result

        except Exception as e:
            logger.error(f"Spectral envelope transfer failed: {e}")
            return source

    def _modify_pitch(self, audio: np.ndarray, sr: int, voice_features: dict, strength: float) -> np.ndarray:
        """Modify pitch to match reference voice characteristics."""
        try:
            import numpy as np
            from scipy import signal

            # Get target F0 characteristics
            target_f0_mean = voice_features.get('f0_mean', 0)

            if target_f0_mean <= 0:
                return audio

            # Estimate current F0 (basic method)
            # Use autocorrelation for pitch detection
            def estimate_pitch(audio_segment, sr):
                # Autocorrelation
                correlation = np.correlate(audio_segment, audio_segment, mode='full')
                correlation = correlation[len(correlation)//2:]

                # Find the first peak after the zero lag
                min_period = int(sr / 400)  # 400 Hz max
                max_period = int(sr / 50)   # 50 Hz min

                if len(correlation) > max_period:
                    peak_idx = np.argmax(correlation[min_period:max_period]) + min_period
                    if peak_idx > 0:
                        return sr / peak_idx
                return 0

            # Estimate current pitch
            current_f0 = estimate_pitch(audio, sr)

            if current_f0 <= 0:
                return audio

            # Calculate pitch shift ratio
            pitch_ratio = target_f0_mean / current_f0

            # Apply strength factor
            pitch_ratio = 1.0 + (pitch_ratio - 1.0) * strength

            # Simple pitch shifting using resampling (basic method)
            if abs(pitch_ratio - 1.0) > 0.01:  # Only if significant change
                # Resample to change pitch
                new_length = int(len(audio) / pitch_ratio)
                pitched_audio = signal.resample(audio, new_length)

                # Pad or trim to original length
                if len(pitched_audio) > len(audio):
                    pitched_audio = pitched_audio[:len(audio)]
                else:
                    pitched_audio = np.pad(pitched_audio, (0, len(audio) - len(pitched_audio)), 'constant')

                return pitched_audio

            return audio

        except Exception as e:
            logger.error(f"Pitch modification failed: {e}")
            return audio

    def _shift_formants(self, audio: np.ndarray, sr: int, voice_features: dict, strength: float) -> np.ndarray:
        """Shift formants to match reference voice characteristics."""
        try:
            import numpy as np
            from scipy import signal

            # Get target spectral characteristics
            target_centroid = voice_features.get('spectral_centroid_mean', 0)

            if target_centroid <= 0:
                return audio

            # Estimate current spectral centroid
            fft = np.fft.rfft(audio)
            magnitude = np.abs(fft)
            freqs = np.fft.rfftfreq(len(audio), 1/sr)

            current_centroid = np.sum(freqs * magnitude) / np.sum(magnitude)

            if current_centroid <= 0:
                return audio

            # Calculate formant shift ratio
            formant_ratio = target_centroid / current_centroid

            # Apply strength factor
            formant_ratio = 1.0 + (formant_ratio - 1.0) * strength * 0.5  # Reduce strength for formants

            # Apply formant shifting by frequency domain manipulation
            if abs(formant_ratio - 1.0) > 0.01:
                # Shift the spectrum
                new_freqs = freqs * formant_ratio

                # Interpolate to new frequency grid
                new_magnitude = np.interp(freqs, new_freqs, magnitude, left=0, right=0)

                # Reconstruct with original phase
                phase = np.angle(fft)
                new_fft = new_magnitude * np.exp(1j * phase)

                # IFFT
                shifted_audio = np.fft.irfft(new_fft, len(audio))
                return shifted_audio

            return audio

        except Exception as e:
            logger.error(f"Formant shifting failed: {e}")
            return audio

    def transfer_timbre(self, request: TimbreTransferRequest) -> bytes:
        """Transfer timbre using lightweight audio processing."""
        if not self.is_loaded:
            raise ValueError("Lightweight voice cloning model not loaded")

        try:
            # Save audio files to temp files
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_source:
                temp_source.write(request.source_audio)
                temp_source_path = temp_source.name

            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_target:
                temp_target.write(request.target_voice)
                temp_target_path = temp_target.name

            # Implement lightweight timbre transfer using spectral analysis
            logger.info(f"Lightweight timbre transfer with strength: {request.strength}")

            import wave
            import numpy as np
            from scipy import signal

            # Read source audio (TTS generated)
            with wave.open(temp_source_path, 'rb') as wav_file:
                source_frames = wav_file.readframes(-1)
                sample_rate = wav_file.getframerate()
                channels = wav_file.getnchannels()
                sample_width = wav_file.getsampwidth()

            # Read target voice (reference)
            with wave.open(temp_target_path, 'rb') as wav_file:
                target_frames = wav_file.readframes(-1)
                target_sample_rate = wav_file.getframerate()

            # Convert to numpy arrays
            source_data = np.frombuffer(source_frames, dtype=np.int16).astype(np.float32) / 32768.0
            target_data = np.frombuffer(target_frames, dtype=np.int16).astype(np.float32) / 32768.0

            # Resample target if needed
            if target_sample_rate != sample_rate:
                target_data = signal.resample(target_data, int(len(target_data) * sample_rate / target_sample_rate))

            # Apply spectral envelope transfer
            result_data = self._apply_spectral_transfer(source_data, target_data, request.strength, sample_rate)

            # Ensure we have valid audio data
            if result_data is None or len(result_data) == 0 or np.all(result_data == 0):
                logger.warning("Timbre transfer produced empty audio, using source audio")
                result_data = source_data

            # Convert back to int16
            result_data = np.clip(result_data * 32767, -32768, 32767).astype(np.int16)

            # Write processed audio to temp file
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_output:
                temp_output_path = temp_output.name

            with wave.open(temp_output_path, 'wb') as wav_out:
                wav_out.setnchannels(channels)
                wav_out.setsampwidth(sample_width)
                wav_out.setframerate(sample_rate)
                wav_out.writeframes(result_data.tobytes())

            # Read processed audio
            with open(temp_output_path, 'rb') as f:
                result_audio = f.read()

            # Clean up temp files
            os.unlink(temp_source_path)
            os.unlink(temp_target_path)
            os.unlink(temp_output_path)

            return result_audio

        except Exception as e:
            logger.error(f"Lightweight timbre transfer failed: {e}")
            # Fallback to returning source audio
            return request.source_audio

    def _apply_spectral_transfer(self, source_audio, target_audio, strength, sample_rate):
        """Apply advanced spectral envelope transfer for accurate timbre transfer."""
        try:
            import numpy as np
            from scipy import signal

            logger.info(f"Applying advanced spectral transfer with strength {strength}")

            # Ensure we have valid audio
            if len(source_audio) == 0 or len(target_audio) == 0:
                logger.warning("Empty audio provided, returning source")
                return source_audio

            # If strength is very low, just return source with minimal processing
            if strength < 0.1:
                return source_audio

            # Prepare target audio for analysis
            if len(target_audio) < len(source_audio) // 2:
                # Repeat target audio if too short
                repeats = int(np.ceil(len(source_audio) / (2 * len(target_audio))))
                target_audio = np.tile(target_audio, repeats)

            # Use advanced spectral envelope transfer
            result_audio = self._advanced_spectral_envelope_transfer(
                source_audio, target_audio, strength, sample_rate
            )

            return result_audio

        except Exception as e:
            logger.warning(f"Advanced spectral transfer failed: {e}, falling back to simple method")
            # Fallback to simple method
            try:
                return self._simple_timbre_transfer(source_audio, target_audio[:len(source_audio)], strength)
            except Exception as e2:
                logger.warning(f"Simple transfer also failed: {e2}, returning original audio")
                return source_audio

    def _advanced_spectral_envelope_transfer(self, source_audio, target_audio, strength, sample_rate):
        """Advanced spectral envelope transfer using STFT and envelope matching."""
        try:
            import numpy as np
            from scipy import signal

            # Parameters for STFT
            window_size = 1024
            hop_size = 256
            window = 'hann'

            # Ensure target is long enough
            if len(target_audio) < len(source_audio):
                repeat_times = int(np.ceil(len(source_audio) / len(target_audio)))
                target_audio = np.tile(target_audio, repeat_times)[:len(source_audio)]
            else:
                target_audio = target_audio[:len(source_audio)]

            # Compute STFT for both signals
            f_src, t_src, stft_src = signal.stft(source_audio, fs=sample_rate,
                                               window=window, nperseg=window_size,
                                               noverlap=window_size-hop_size)

            f_tgt, t_tgt, stft_tgt = signal.stft(target_audio, fs=sample_rate,
                                               window=window, nperseg=window_size,
                                               noverlap=window_size-hop_size)

            # Extract magnitude and phase
            mag_src = np.abs(stft_src)
            phase_src = np.angle(stft_src)
            mag_tgt = np.abs(stft_tgt)

            # Compute spectral envelopes using smoothing
            envelope_src = self._compute_spectral_envelope(mag_src)
            envelope_tgt = self._compute_spectral_envelope(mag_tgt)

            # Transfer spectral envelope
            # Blend between source and target envelopes
            envelope_new = envelope_src * (1 - strength) + envelope_tgt * strength

            # Apply envelope transfer while preserving fine spectral details
            # Normalize by source envelope and multiply by target envelope
            epsilon = 1e-10  # Avoid division by zero
            envelope_ratio = envelope_new / (envelope_src + epsilon)

            # Apply the envelope transfer
            mag_new = mag_src * envelope_ratio

            # Reconstruct STFT with original phase
            stft_new = mag_new * np.exp(1j * phase_src)

            # Inverse STFT
            _, result_audio = signal.istft(stft_new, fs=sample_rate,
                                         window=window, nperseg=window_size,
                                         noverlap=window_size-hop_size)

            # Ensure output length matches input
            if len(result_audio) > len(source_audio):
                result_audio = result_audio[:len(source_audio)]
            elif len(result_audio) < len(source_audio):
                result_audio = np.pad(result_audio, (0, len(source_audio) - len(result_audio)), 'constant')

            return result_audio

        except Exception as e:
            logger.error(f"Advanced spectral envelope transfer failed: {e}")
            return source_audio

    def _compute_spectral_envelope(self, magnitude_spectrogram):
        """Compute spectral envelope using smoothing."""
        try:
            import numpy as np
            from scipy import signal

            # Apply smoothing across frequency dimension to get envelope
            # Use median filter followed by Gaussian smoothing
            envelope = np.zeros_like(magnitude_spectrogram)

            for t in range(magnitude_spectrogram.shape[1]):
                # Median filter to remove fine details
                smoothed = signal.medfilt(magnitude_spectrogram[:, t], kernel_size=5)
                # Gaussian smoothing for envelope
                envelope[:, t] = signal.savgol_filter(smoothed, window_length=21, polyorder=3)

            return envelope

        except Exception as e:
            logger.error(f"Spectral envelope computation failed: {e}")
            return magnitude_spectrogram

    def _simple_timbre_transfer(self, source_audio, target_audio, strength):
        """Apply simple but effective timbre transfer."""
        try:
            import numpy as np
            from scipy import signal

            # Calculate basic characteristics
            source_rms = np.sqrt(np.mean(source_audio**2))
            target_rms = np.sqrt(np.mean(target_audio**2))

            if source_rms == 0:
                return source_audio

            # Apply amplitude scaling based on target
            amplitude_factor = target_rms / source_rms if source_rms > 0 else 1.0
            amplitude_factor = 1.0 + strength * (amplitude_factor - 1.0)
            amplitude_factor = np.clip(amplitude_factor, 0.3, 3.0)  # Reasonable bounds

            # Apply the amplitude scaling
            result = source_audio * amplitude_factor

            # Apply simple spectral shaping if strength is high
            if strength > 0.5:
                # Simple high-frequency emphasis/de-emphasis based on target
                try:
                    # Design a simple filter based on spectral characteristics
                    nyquist = 22050 / 2  # Assuming 22050 Hz sample rate

                    # Simple spectral tilt adjustment
                    if np.mean(np.abs(target_audio)) > np.mean(np.abs(source_audio)):
                        # Target is brighter, add some high-frequency emphasis
                        sos = signal.butter(2, 4000 / nyquist, btype='high', output='sos')
                        high_freq = signal.sosfilt(sos, result)
                        result = result + 0.1 * strength * high_freq
                    else:
                        # Target is darker, add some low-frequency emphasis
                        sos = signal.butter(2, 2000 / nyquist, btype='low', output='sos')
                        low_freq = signal.sosfilt(sos, result)
                        result = result + 0.1 * strength * low_freq

                except Exception as e:
                    logger.warning(f"Spectral shaping failed: {e}")
                    # Continue with just amplitude scaling

            # Ensure no clipping
            max_val = np.max(np.abs(result))
            if max_val > 1.0:
                result = result / max_val * 0.95

            return result

        except Exception as e:
            logger.warning(f"Simple timbre transfer failed: {e}")
            return source_audio



class SeedVCCloner(BaseVoiceCloner):
    """SeedVC implementation for voice cloning and timbre transfer."""

    def load_model(self) -> bool:
        """Load SeedVC model."""
        try:
            # Try to import SeedVC
            try:
                import torch
                # This would be the actual SeedVC import
                # from seed_vc import SeedVC
                logger.info("SeedVC dependencies available")
                self.is_loaded = True
                return True
            except ImportError:
                logger.warning("SeedVC not installed. Install with: pip install seed-vc")
                return False

        except Exception as e:
            logger.error(f"Failed to load SeedVC: {e}")
            return False

    def clone_voice(self, request: VoiceCloneRequest) -> bytes:
        """Clone voice using SeedVC."""
        if not self.is_loaded:
            raise ValueError("SeedVC model not loaded")

        try:
            # Save reference audio to temp file
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_ref:
                temp_ref.write(request.reference_audio)
                temp_ref_path = temp_ref.name

            # Placeholder for SeedVC voice cloning
            # In real implementation, this would use SeedVC's API
            logger.info(f"SeedVC voice cloning for text: {request.text[:50]}...")

            # For now, return the reference audio (placeholder)
            audio_bytes = request.reference_audio

            # Clean up temp file
            os.unlink(temp_ref_path)

            return audio_bytes

        except Exception as e:
            logger.error(f"SeedVC voice cloning failed: {e}")
            raise

    def transfer_timbre(self, request: TimbreTransferRequest) -> bytes:
        """Transfer timbre using SeedVC."""
        if not self.is_loaded:
            raise ValueError("SeedVC model not loaded")

        try:
            # Save audio files to temp files
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_source:
                temp_source.write(request.source_audio)
                temp_source_path = temp_source.name

            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_target:
                temp_target.write(request.target_voice)
                temp_target_path = temp_target.name

            # Implement advanced SeedVC-style timbre transfer
            logger.info(f"SeedVC timbre transfer with strength: {request.strength}")

            import wave
            import numpy as np
            from scipy import signal

            # Read source audio
            with wave.open(temp_source_path, 'rb') as wav_file:
                frames = wav_file.readframes(-1)
                sample_rate = wav_file.getframerate()
                channels = wav_file.getnchannels()
                sample_width = wav_file.getsampwidth()

            # Read target voice for characteristics
            with wave.open(temp_target_path, 'rb') as wav_file:
                target_frames = wav_file.readframes(-1)
                target_sample_rate = wav_file.getframerate()

            # Convert to numpy arrays
            source_data = np.frombuffer(frames, dtype=np.int16).astype(np.float32) / 32768.0
            target_data = np.frombuffer(target_frames, dtype=np.int16).astype(np.float32) / 32768.0

            # Resample target if needed
            if target_sample_rate != sample_rate:
                target_data = signal.resample(target_data, int(len(target_data) * sample_rate / target_sample_rate))

            # Apply simple but effective timbre transfer
            result_data = self._simple_timbre_transfer(source_data, target_data, request.strength)

            # Ensure we have valid audio data
            if result_data is None or len(result_data) == 0 or np.all(result_data == 0):
                logger.warning("SeedVC timbre transfer produced empty audio, using source audio")
                result_data = source_data

            # Convert back to int16
            result_audio_int = np.clip(result_data * 32767, -32768, 32767).astype(np.int16)

            # Write processed audio to temp file
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_output:
                temp_output_path = temp_output.name

            with wave.open(temp_output_path, 'wb') as wav_out:
                wav_out.setnchannels(channels)
                wav_out.setsampwidth(sample_width)
                wav_out.setframerate(sample_rate)
                wav_out.writeframes(result_audio_int.tobytes())

            # Read processed audio
            with open(temp_output_path, 'rb') as f:
                result_audio = f.read()

            # Clean up temp output file
            os.unlink(temp_output_path)

            # Clean up temp files
            os.unlink(temp_source_path)
            os.unlink(temp_target_path)

            return result_audio

        except Exception as e:
            logger.error(f"SeedVC timbre transfer failed: {e}")
            raise



class BarkCloner(BaseVoiceCloner):
    """Bark implementation for voice cloning."""

    def load_model(self) -> bool:
        """Load Bark model."""
        try:
            # Try to import Bark
            try:
                from bark import SAMPLE_RATE, generate_audio, preload_models
                from bark.generation import SUPPORTED_LANGS
                self.bark_generate = generate_audio
                self.bark_preload = preload_models
                self.sample_rate = SAMPLE_RATE
                self.supported_langs = SUPPORTED_LANGS

                # Preload models
                self.bark_preload()
                self.is_loaded = True
                logger.info("Bark model loaded successfully")
                return True

            except ImportError:
                logger.warning("Bark not installed. Install with: pip install bark")
                return False

        except Exception as e:
            logger.error(f"Failed to load Bark: {e}")
            return False

    def clone_voice(self, request: VoiceCloneRequest) -> bytes:
        """Clone voice using Bark."""
        if not self.is_loaded:
            raise ValueError("Bark model not loaded")

        try:
            # Bark uses text prompts for voice cloning
            # This is a simplified implementation
            audio_array = self.bark_generate(
                text=request.text,
                history_prompt=None,  # Would need to process reference audio
                text_temp=0.7,
                waveform_temp=0.7
            )

            # Convert to WAV bytes
            with io.BytesIO() as wav_io:
                import soundfile as sf
                sf.write(wav_io, audio_array, self.sample_rate, format='WAV')
                return wav_io.getvalue()

        except Exception as e:
            logger.error(f"Bark voice cloning failed: {e}")
            raise

class TortoiseCloner(BaseVoiceCloner):
    """Tortoise TTS implementation for voice cloning."""

    def load_model(self) -> bool:
        """Load Tortoise TTS model."""
        try:
            # Try to import Tortoise TTS
            try:
                from tortoise.api import TextToSpeech
                from tortoise.utils.audio import load_audio
                self.tts = TextToSpeech()
                self.load_audio = load_audio
                self.is_loaded = True
                logger.info("Tortoise TTS model loaded successfully")
                return True

            except ImportError:
                logger.warning("Tortoise TTS not installed. Install with: pip install tortoise-tts")
                return False

        except Exception as e:
            logger.error(f"Failed to load Tortoise TTS: {e}")
            return False

    def clone_voice(self, request: VoiceCloneRequest) -> bytes:
        """Clone voice using Tortoise TTS."""
        if not self.is_loaded:
            raise ValueError("Tortoise TTS model not loaded")

        try:
            # Save reference audio to temp file
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_ref:
                temp_ref.write(request.reference_audio)
                temp_ref_path = temp_ref.name

            # Load reference audio
            reference_audio = self.load_audio(temp_ref_path, 22050)

            # Generate audio
            gen = self.tts.tts_with_preset(
                request.text,
                voice_samples=[reference_audio],
                preset='fast'  # or 'standard', 'high_quality'
            )

            # Convert to WAV bytes
            with io.BytesIO() as wav_io:
                import soundfile as sf
                sf.write(wav_io, gen.squeeze().cpu().numpy(), 22050, format='WAV')
                audio_bytes = wav_io.getvalue()

            # Clean up temp file
            os.unlink(temp_ref_path)

            return audio_bytes

        except Exception as e:
            logger.error(f"Tortoise TTS voice cloning failed: {e}")
            raise

class ValleXCloner(BaseVoiceCloner):
    """VALL-E X implementation for voice cloning."""

    def load_model(self) -> bool:
        """Load VALL-E X model."""
        try:
            # VALL-E X is more complex to implement
            # This is a placeholder for the actual implementation
            logger.warning("VALL-E X implementation placeholder")
            self.is_loaded = True
            return True

        except Exception as e:
            logger.error(f"Failed to load VALL-E X: {e}")
            return False

    def clone_voice(self, request: VoiceCloneRequest) -> bytes:
        """Clone voice using VALL-E X."""
        if not self.is_loaded:
            raise ValueError("VALL-E X model not loaded")

        # Placeholder implementation
        logger.warning("VALL-E X voice cloning not yet implemented")
        return request.reference_audio  # Return reference for now

class VoiceCloningManager:
    """Manager for voice cloning and timbre transfer operations."""
    
    def __init__(self):
        self.cloners: Dict[VoiceCloningModel, BaseVoiceCloner] = {}
        self.executor = ThreadPoolExecutor(max_workers=2)
        self.initialize_cloners()
    
    def initialize_cloners(self):
        """Initialize lightweight voice cloning models (OpenVoice v2 only)."""
        logger.info("Initializing lightweight voice cloning models...")

        # Only initialize OpenVoice v2 for lightweight setup
        try:
            openvoice_cloner = OpenVoiceCloner()
            if openvoice_cloner.load_model():
                self.cloners[VoiceCloningModel.OPENVOICE_V2] = openvoice_cloner
                logger.info("OpenVoice v2 model loaded successfully")
            else:
                logger.warning("OpenVoice v2 model failed to load")
        except Exception as e:
            logger.error(f"Failed to initialize OpenVoice v2: {e}")

        # Skip heavy models to avoid large downloads and dependency conflicts
        logger.info("Skipping heavy models (F5-TTS, XTTS v2, Bark, etc.) for lightweight setup")

        logger.info(f"Initialized {len(self.cloners)} voice cloning models: {list(self.cloners.keys())}")
    
    def get_available_models(self) -> List[str]:
        """Get list of available voice cloning models."""
        return [model.value for model in self.cloners.keys()]

    async def load_model(self, model_name: str) -> bool:
        """Load a specific voice cloning model."""
        try:
            # Convert string to enum
            model_enum = VoiceCloningModel(model_name)

            if model_enum not in self.cloners:
                raise ValueError(f"Model {model_name} not available")

            cloner = self.cloners[model_enum]

            # Load the model in a thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            success = await loop.run_in_executor(
                self.executor,
                cloner.load_model
            )

            return success

        except Exception as e:
            logger.error(f"Failed to load model {model_name}: {e}")
            return False

    async def clone_voice_async(self, request: VoiceCloneRequest) -> bytes:
        """Asynchronously clone voice."""
        if request.model_type not in self.cloners:
            raise ValueError(f"Model {request.model_type.value} not available")
        
        cloner = self.cloners[request.model_type]
        loop = asyncio.get_event_loop()
        
        return await loop.run_in_executor(
            self.executor,
            cloner.clone_voice,
            request
        )
    
    async def transfer_timbre_async(self, request: TimbreTransferRequest) -> bytes:
        """Asynchronously transfer timbre."""
        if request.model_type not in self.cloners:
            raise ValueError(f"Model {request.model_type.value} not available")
        
        cloner = self.cloners[request.model_type]
        if not hasattr(cloner, 'transfer_timbre'):
            raise ValueError(f"Model {request.model_type.value} doesn't support timbre transfer")
        
        loop = asyncio.get_event_loop()
        
        return await loop.run_in_executor(
            self.executor,
            cloner.transfer_timbre,
            request
        )
    
    def clone_voice_sync(self, request: VoiceCloneRequest) -> bytes:
        """Synchronously clone voice."""
        if request.model_type not in self.cloners:
            raise ValueError(f"Model {request.model_type.value} not available")
        
        cloner = self.cloners[request.model_type]
        return cloner.clone_voice(request)
    
    def transfer_timbre_sync(self, request: TimbreTransferRequest) -> bytes:
        """Synchronously transfer timbre."""
        if request.model_type not in self.cloners:
            raise ValueError(f"Model {request.model_type.value} not available")
        
        cloner = self.cloners[request.model_type]
        if not hasattr(cloner, 'transfer_timbre'):
            raise ValueError(f"Model {request.model_type.value} doesn't support timbre transfer")
        
        return cloner.transfer_timbre(request)
