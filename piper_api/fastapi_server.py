#!/usr/bin/env python3
"""
FastAPI server for Piper TTS using the official Piper implementation.
"""

import io
import logging
import wave
import asyncio
from typing import Optional, List
from concurrent.futures import ThreadPoolExecutor

from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Query, BackgroundTasks
from fastapi.responses import Response
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field

try:
    from .voice import <PERSON>Voice
    from .model_manager import ModelManager
    from .voice_cloning_endpoints import voice_cloning_manager
    from .timbre_style_transfer import TimbreStyleManager
except ImportError:
    from voice import <PERSON>Voice
    from model_manager import Model<PERSON>anager
    from voice_cloning_endpoints import voice_cloning_manager
    from timbre_style_transfer import <PERSON>bre<PERSON>tyleManager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Piper TTS API",
    description="Text-to-Speech API using official Piper implementation with speaker and speech rate control",
    version="2.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],  # React dev server
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class VoiceManager:
    """Optimized voice manager for scalable inference"""
    def __init__(self):
        self.voice: Optional[PiperVoice] = None
        self.voice_config = {}
        self.is_loading = False
        self.model_manager = ModelManager("models")
        self.timbre_style_manager = TimbreStyleManager(self)

    def load_voice(self, model_path: str, config_path: Optional[str] = None, use_cuda: bool = False):
        """Load voice with optimization for inference"""
        if self.is_loading:
            return False

        self.is_loading = True
        try:
            # Load with optimized ONNX session options
            self.voice = PiperVoice.load(model_path, config_path=config_path, use_cuda=use_cuda)
            self.voice_config = {
                "model_path": model_path,
                "config_path": config_path or f"{model_path}.json",
                "use_cuda": use_cuda
            }
            logger.info(f"Voice loaded: speakers={self.voice.config.num_speakers}, sample_rate={self.voice.config.sample_rate}")

            # Initialize timbre and style transfer
            try:
                self.timbre_style_manager.initialize()
                logger.info("Timbre and style transfer initialized")
            except Exception as e:
                logger.warning(f"Failed to initialize timbre/style transfer: {e}")

            return True
        except Exception as e:
            logger.error(f"Failed to load voice: {e}")
            return False
        finally:
            self.is_loading = False

    def synthesize_audio(self, text: str, speaker_id: Optional[int] = None,
                        length_scale: Optional[float] = None, noise_scale: Optional[float] = None,
                        noise_w: Optional[float] = None, sentence_silence: float = 0.0) -> bytes:
        """Optimized synthesis function"""
        if not self.voice:
            raise ValueError("No voice loaded")

        with io.BytesIO() as wav_io:
            with wave.open(wav_io, "wb") as wav_file:
                self.voice.synthesize(
                    text=text,
                    wav_file=wav_file,
                    speaker_id=speaker_id,
                    length_scale=length_scale,
                    noise_scale=noise_scale,
                    noise_w=noise_w,
                    sentence_silence=sentence_silence
                )
            return wav_io.getvalue()

# Global voice manager and thread pool for CPU-bound inference
voice_manager = VoiceManager()
executor = ThreadPoolExecutor(max_workers=2)  # Limit to prevent memory issues with ONNX

# Pydantic models for request/response

class UnifiedGenerateRequest(BaseModel):
    text: str = Field(..., description="Text to synthesize", min_length=1, max_length=2000)

    # Basic TTS options
    speaker_id: Optional[int] = Field(None, description="Speaker ID", ge=0)
    speech_rate: Optional[float] = Field(1.0, description="Speech rate (0.5 = slower, 2.0 = faster)", gt=0.1, le=3.0)
    noise_scale: Optional[float] = Field(None, description="Generator noise (0.0-1.0)")
    noise_w: Optional[float] = Field(None, description="Phoneme width noise (0.0-1.0)")
    sentence_silence: Optional[float] = Field(0.0, description="Seconds of silence after each sentence", ge=0.0, le=2.0)

    # Voice cloning options
    use_voice_cloning: bool = Field(False, description="Enable voice cloning")
    reference_audio_base64: Optional[str] = Field(None, description="Base64 encoded reference audio for voice cloning")
    cloning_model: str = Field("f5_tts", description="Voice cloning model (f5_tts, xtts_v2, openvoice_v2)")
    cloning_strength: float = Field(1.0, description="Voice cloning strength", ge=0.1, le=2.0)
    language: str = Field("ne", description="Language code")
    emotion: Optional[str] = Field(None, description="Emotion to apply (if supported)")

    # Timbre/Style transfer options
    use_timbre_transfer: bool = Field(False, description="Enable timbre transfer")
    timbre_strength: float = Field(1.0, description="Timbre transfer strength", ge=0.1, le=2.0)
    preserve_prosody: bool = Field(True, description="Preserve original prosody in timbre transfer")

class VoiceInfo(BaseModel):
    num_speakers: int
    sample_rate: int
    espeak_voice: str
    phoneme_type: str
    length_scale: float
    noise_scale: float
    noise_w: float

class SpeakerInfo(BaseModel):
    speaker_id: int
    total_speakers: int

# Remove old load_voice function - now handled by VoiceManager

@app.on_event("startup")
async def startup_event():
    """Initialize the voice on startup."""
    # Get the default model from model manager
    default_model_id = voice_manager.model_manager.get_default_model_id()

    if default_model_id:
        model_paths = voice_manager.model_manager.get_model_paths(default_model_id)
        if model_paths:
            model_path, config_path = model_paths
            logger.info(f"Loading default model: {default_model_id}")
            if voice_manager.load_voice(model_path, config_path):
                voice_manager.model_manager.set_current_model(default_model_id)
                logger.info(f"Successfully loaded default model: {default_model_id}")
            else:
                logger.warning(f"Failed to load default model: {default_model_id}")
        else:
            logger.warning(f"Model paths not found for: {default_model_id}")
    else:
        logger.warning("No models found. Add models to the 'models' directory.")

@app.get("/health", summary="Health check")
async def root():
    """Health check endpoint."""
    current_model = voice_manager.model_manager.get_current_model()
    return {
        "message": "Piper TTS API is running",
        "status": "healthy",
        "voice_loaded": voice_manager.voice is not None,
        "current_model": current_model.name if current_model else None,
        "available_models": len(voice_manager.model_manager.available_models)
    }

@app.get("/models", summary="Get available models")
async def get_available_models():
    """Get list of all available models."""
    return {
        "models": voice_manager.model_manager.get_available_models(),
        "current_model": voice_manager.model_manager.current_model_id
    }

@app.post("/models/{model_id}/load", summary="Load a specific model")
async def load_model(
    model_id: str,
    use_cuda: bool = Query(False, description="Use CUDA acceleration")
):
    """Load a specific model by ID."""
    model_paths = voice_manager.model_manager.get_model_paths(model_id)
    if not model_paths:
        raise HTTPException(status_code=404, detail=f"Model not found: {model_id}")

    model_path, config_path = model_paths
    success = voice_manager.load_voice(model_path, config_path, use_cuda)
    if not success:
        raise HTTPException(status_code=500, detail=f"Failed to load model: {model_id}")

    voice_manager.model_manager.set_current_model(model_id)
    model_info = voice_manager.model_manager.get_model_info(model_id)

    return {
        "message": f"Model '{model_info.name}' loaded successfully",
        "model_id": model_id,
        "model_info": {
            "name": model_info.name,
            "description": model_info.description,
            "type": model_info.type,
            "quality": model_info.quality,
            "speakers": model_info.speakers,
            "sample_rate": model_info.sample_rate
        },
        "voice_info": await get_voice_info()
    }

@app.post("/models/refresh", summary="Refresh available models")
async def refresh_models():
    """Refresh the list of available models."""
    old_count = len(voice_manager.model_manager.available_models)
    voice_manager.model_manager.refresh_models()
    new_count = len(voice_manager.model_manager.available_models)

    return {
        "message": "Models refreshed successfully",
        "models_found": new_count,
        "models_added": new_count - old_count,
        "models": voice_manager.model_manager.get_available_models()
    }

@app.get("/models/stats", summary="Get model statistics")
async def get_model_stats():
    """Get statistics about available models."""
    return voice_manager.model_manager.get_model_stats()

@app.get("/models/check-new", summary="Check for new models")
async def check_new_models():
    """Check if there are new models available."""
    has_new = voice_manager.model_manager.has_new_models()
    return {
        "has_new_models": has_new,
        "message": "New models detected" if has_new else "No new models found",
        "last_scan": voice_manager.model_manager.last_scan_time
    }

@app.post("/models/auto-scan/{action}", summary="Control auto-scanning")
async def control_auto_scan(action: str):
    """Start or stop automatic model scanning."""
    if action.lower() == "start":
        voice_manager.model_manager.start_auto_scan()
        return {"message": "Auto-scanning started", "status": "enabled"}
    elif action.lower() == "stop":
        voice_manager.model_manager.stop_auto_scan()
        return {"message": "Auto-scanning stopped", "status": "disabled"}
    else:
        raise HTTPException(status_code=400, detail="Action must be 'start' or 'stop'")



@app.get("/voice/info", response_model=VoiceInfo, summary="Get voice information")
async def get_voice_info():
    """Get information about the loaded voice."""
    if voice_manager.voice is None:
        raise HTTPException(status_code=500, detail="No voice loaded")

    return VoiceInfo(
        num_speakers=voice_manager.voice.config.num_speakers,
        sample_rate=voice_manager.voice.config.sample_rate,
        espeak_voice=voice_manager.voice.config.espeak_voice,
        phoneme_type=voice_manager.voice.config.phoneme_type.value,
        length_scale=voice_manager.voice.config.length_scale,
        noise_scale=voice_manager.voice.config.noise_scale,
        noise_w=voice_manager.voice.config.noise_w
    )

@app.get("/speakers", summary="Get speaker information")
async def get_speakers():
    """Get information about available speakers."""
    if voice_manager.voice is None:
        raise HTTPException(status_code=500, detail="No voice loaded")

    return {
        "num_speakers": voice_manager.voice.config.num_speakers,
        "speakers": [{"speaker_id": i} for i in range(voice_manager.voice.config.num_speakers)]
    }

@app.get("/features", summary="Get available features and capabilities")
async def get_available_features():
    """Get information about available TTS features and models."""
    try:
        # Check voice cloning availability
        voice_cloning_models = []
        voice_cloning_enabled = False
        try:
            voice_cloning_models = voice_cloning_manager.get_available_models()
            voice_cloning_enabled = len(voice_cloning_models) > 0
        except Exception as e:
            logger.warning(f"Voice cloning check failed: {e}")

        # Check timbre transfer availability
        timbre_transfer_enabled = False
        timbre_transfer_models = []
        try:
            # Timbre transfer is available if we have voice cloning models that support it
            if voice_cloning_enabled and voice_cloning_models:
                # Check which models support timbre transfer
                supported_models = []
                for model in voice_cloning_models:
                    if model in ["openvoice_v2", "seed_vc", "xtts_v2"]:
                        supported_models.append(model)

                if supported_models:
                    timbre_transfer_enabled = True
                    timbre_transfer_models = supported_models

            # Also check if the timbre_style_manager is available
            if hasattr(voice_manager, 'timbre_style_manager') and voice_manager.timbre_style_manager:
                if voice_manager.timbre_style_manager.is_loaded:
                    timbre_transfer_enabled = True
                    if "piper_native" not in timbre_transfer_models:
                        timbre_transfer_models.append("piper_native")
        except Exception as e:
            logger.warning(f"Timbre transfer check failed: {e}")

        # Get current voice info
        current_voice_info = None
        if voice_manager.voice:
            current_voice_info = {
                "num_speakers": voice_manager.voice.config.num_speakers,
                "sample_rate": voice_manager.voice.config.sample_rate,
                "supports_multi_speaker": voice_manager.voice.config.num_speakers > 1
            }

        return {
            "voice_cloning": {
                "enabled": voice_cloning_enabled,
                "available_models": voice_cloning_models,
                "supported_languages": ["en", "es", "fr", "de", "it", "pt", "pl", "tr", "ru", "nl", "cs", "ar", "zh", "ja", "hu", "ko", "ne"] if voice_cloning_enabled else []
            },
            "timbre_transfer": {
                "enabled": timbre_transfer_enabled,
                "available_models": timbre_transfer_models,
                "supports_speaker_id": True,
                "supports_prosody_preservation": True
            },
            "style_transfer": {
                "enabled": False,  # Not implemented yet
                "available_emotions": [],
                "supports_prosody_control": False
            },
            "current_voice": current_voice_info,
            "unified_endpoint": "/generate"
        }
    except Exception as e:
        logger.error(f"Error getting features: {e}")
        return {
            "voice_cloning": {"enabled": False, "available_models": [], "supported_languages": []},
            "timbre_transfer": {"enabled": False, "available_models": [], "supports_speaker_id": False, "supports_prosody_preservation": False},
            "style_transfer": {"enabled": False, "available_emotions": [], "supports_prosody_control": False},
            "current_voice": None,
            "unified_endpoint": "/generate"
        }





@app.post("/generate", summary="Unified audio generation endpoint")
async def generate_audio(request: UnifiedGenerateRequest):
    """
    Unified endpoint for all audio generation features:
    - Regular TTS synthesis
    - Voice cloning
    - Timbre/style transfer

    All options are available in a single request with automatic fallback to regular TTS.
    """
    logger.info(f"Generate request: text='{request.text[:50]}...', voice_cloning={request.use_voice_cloning}, timbre_transfer={request.use_timbre_transfer}")

    # Track what method was actually used for response headers
    generation_method = "regular_tts"
    fallback_reason = None

    try:
        # Step 1: Always generate base TTS audio first
        if voice_manager.voice is None:
            raise HTTPException(status_code=500, detail="No voice loaded")

        # Validate speaker ID for base synthesis
        if request.speaker_id is not None:
            if request.speaker_id < 0 or request.speaker_id >= voice_manager.voice.config.num_speakers:
                raise HTTPException(
                    status_code=400,
                    detail=f"Speaker ID must be between 0 and {voice_manager.voice.config.num_speakers - 1}"
                )

        # Convert speech_rate to length_scale
        length_scale = 1.0 / request.speech_rate if request.speech_rate else None

        # Generate base TTS audio with Piper
        logger.info(f"Generating base TTS audio for text: '{request.text[:50]}...'")
        loop = asyncio.get_event_loop()
        base_tts_audio = await loop.run_in_executor(
            executor,
            voice_manager.synthesize_audio,
            request.text,
            request.speaker_id,
            length_scale,
            request.noise_scale,
            request.noise_w,
            request.sentence_silence or 0.0
        )

        logger.info(f"Base TTS audio generated: {len(base_tts_audio)} bytes")

        # Step 2: Apply voice cloning if requested
        if request.use_voice_cloning and request.reference_audio_base64:
            import base64
            try:
                from .voice_cloning_manager import VoiceCloneRequest, VoiceCloningModel
            except ImportError:
                from voice_cloning_manager import VoiceCloneRequest, VoiceCloningModel

            # Check if voice cloning is available
            available_models = voice_cloning_manager.get_available_models()
            if not available_models:
                fallback_reason = "No voice cloning models available"
                logger.warning(f"{fallback_reason}. Using base TTS audio.")
            elif request.cloning_model not in available_models:
                fallback_reason = f"Requested model '{request.cloning_model}' not available"
                logger.warning(f"{fallback_reason}. Available: {available_models}. Using base TTS audio.")
            else:
                try:
                    # Decode reference audio
                    reference_audio_bytes = base64.b64decode(request.reference_audio_base64)
                    if len(reference_audio_bytes) == 0:
                        raise ValueError("Reference audio is empty")

                    # Validate cloning model
                    try:
                        cloning_model_enum = VoiceCloningModel(request.cloning_model)
                    except ValueError:
                        raise HTTPException(
                            status_code=400,
                            detail=f"Invalid cloning_model. Available models: {available_models}"
                        )

                    # Apply proper voice cloning
                    clone_request = VoiceCloneRequest(
                        text=request.text,
                        reference_audio=reference_audio_bytes,
                        model_type=cloning_model_enum,
                        speaker_id=request.speaker_id,
                        language=request.language,
                        speed=request.speech_rate or 1.0,
                        emotion=request.emotion,
                        timbre_strength=request.cloning_strength
                    )

                    # Perform voice cloning
                    cloned_audio = await voice_cloning_manager.clone_voice_async(clone_request)
                    generation_method = "voice_cloning"

                    logger.info(f"Voice cloning applied: {len(cloned_audio)} bytes")
                    return Response(
                        content=cloned_audio,
                        media_type="audio/wav",
                        headers={
                            "Content-Disposition": f"attachment; filename=generated_voice_cloned_{request.cloning_model}.wav",
                            "X-Generation-Method": generation_method,
                            "X-Cloning-Model": request.cloning_model,
                            "X-Base-TTS-Size": str(len(base_tts_audio))
                        }
                    )

                except Exception as e:
                    fallback_reason = f"Voice cloning failed: {str(e)}"
                    logger.error(fallback_reason)
                    logger.warning("Using base TTS audio.")

        # Step 3: Apply timbre transfer if requested (and voice cloning wasn't applied)
        elif request.use_timbre_transfer and request.reference_audio_base64:
            # Check if timbre transfer is available
            timbre_available = (hasattr(voice_manager, 'timbre_style_manager') and
                              voice_manager.timbre_style_manager and
                              hasattr(voice_manager.timbre_style_manager, 'is_loaded') and
                              voice_manager.timbre_style_manager.is_loaded)

            # Also check voice cloning models for timbre transfer
            available_models = voice_cloning_manager.get_available_models()
            voice_cloning_timbre_available = len(available_models) > 0

            if not timbre_available and not voice_cloning_timbre_available:
                fallback_reason = "Timbre transfer not available"
                logger.warning(f"{fallback_reason}. Using base TTS audio.")
            else:
                try:
                    # Apply timbre transfer using voice cloning models
                    import base64
                    try:
                        from .voice_cloning_manager import TimbreTransferRequest, VoiceCloningModel
                    except ImportError:
                        from voice_cloning_manager import TimbreTransferRequest, VoiceCloningModel

                    reference_audio_bytes = base64.b64decode(request.reference_audio_base64)
                    if len(reference_audio_bytes) == 0:
                        raise ValueError("Reference audio is empty")

                    # Use the first available model for timbre transfer
                    model_to_use = request.cloning_model if request.cloning_model in available_models else available_models[0]

                    # Create timbre transfer request using the base TTS audio
                    transfer_request = TimbreTransferRequest(
                        source_audio=base_tts_audio,
                        target_voice=reference_audio_bytes,
                        model_type=VoiceCloningModel(model_to_use),
                        strength=request.timbre_strength,
                        preserve_prosody=request.preserve_prosody
                    )

                    # Perform timbre transfer
                    transferred_audio = await voice_cloning_manager.transfer_timbre_async(transfer_request)
                    generation_method = "timbre_transfer"

                    logger.info(f"Timbre transfer successful: {len(transferred_audio)} bytes")
                    return Response(
                        content=transferred_audio,
                        media_type="audio/wav",
                        headers={
                            "Content-Disposition": f"attachment; filename=generated_timbre_transfer.wav",
                            "X-Generation-Method": generation_method,
                            "X-Transfer-Strength": str(request.timbre_strength),
                            "X-Transfer-Model": model_to_use,
                            "X-Base-TTS-Size": str(len(base_tts_audio))
                        }
                    )

                except Exception as e:
                    fallback_reason = f"Timbre transfer failed: {str(e)}"
                    logger.error(fallback_reason)
                    logger.warning("Using base TTS audio.")

        # Step 4: Return base TTS audio (default and fallback)
        logger.info(f"Returning base TTS audio: {len(base_tts_audio)} bytes")

        # Prepare response headers
        headers = {
            "Content-Disposition": "attachment; filename=generated_speech.wav",
            "Content-Length": str(len(base_tts_audio)),
            "X-Generation-Method": generation_method
        }

        # Add fallback information if applicable
        if fallback_reason:
            headers["X-Fallback-Reason"] = fallback_reason

        return Response(
            content=base_tts_audio,
            media_type="audio/wav",
            headers=headers
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error during audio generation: {e}")
        raise HTTPException(status_code=500, detail=f"Audio generation failed: {str(e)}")

# Note: Voice cloning and timbre transfer are now handled through the unified /generate endpoint
# Separate endpoints have been removed to simplify the API

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000,reload=True)
