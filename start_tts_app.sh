#!/bin/bash

# TTS Application Startup Script
# Starts both the Piper API backend and React frontend

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[TTS-APP]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to cleanup processes on exit
cleanup() {
    print_status "Shutting down TTS application..."
    if [ ! -z "$BACKEND_PID" ]; then
        print_status "Stopping backend (PID: $BACKEND_PID)..."
        kill $BACKEND_PID 2>/dev/null || true
    fi
    if [ ! -z "$FRONTEND_PID" ]; then
        print_status "Stopping frontend (PID: $FRONTEND_PID)..."
        kill $FRONTEND_PID 2>/dev/null || true
    fi
    print_success "TTS application stopped"
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

print_status "Starting TTS Application..."
print_status "Script directory: $SCRIPT_DIR"

# Check if virtual environment exists
if [ ! -d ".venv" ]; then
    print_error "Virtual environment not found. Please run setup first."
    exit 1
fi

# Check if node_modules exists for frontend
if [ ! -d "piper_frontend/node_modules" ]; then
    print_warning "Frontend dependencies not found. Installing..."
    cd piper_frontend
    npm install
    cd ..
fi

# Check if models directory exists
if [ ! -d "piper_api/models" ]; then
    print_error "Models directory not found. Please set up models first."
    exit 1
fi

# Count available models
MODEL_COUNT=$(find piper_api/models -name "model.onnx" | wc -l)
if [ "$MODEL_COUNT" -eq 0 ]; then
    print_error "No models found in piper_api/models/. Please add models first."
    exit 1
fi

print_success "Found $MODEL_COUNT TTS models"

# Start backend
print_status "Starting Piper TTS API backend..."
cd piper_api
../.venv/bin/python -m uvicorn fastapi_server:app --host 0.0.0.0 --port 8000 &
BACKEND_PID=$!
cd ..

# Wait a moment for backend to start
sleep 3

# Check if backend is running
if ! kill -0 $BACKEND_PID 2>/dev/null; then
    print_error "Backend failed to start"
    exit 1
fi

# Test backend health
print_status "Testing backend connection..."
for i in {1..10}; do
    if curl -s http://localhost:8000/health > /dev/null 2>&1; then
        print_success "Backend is running on http://localhost:8000"
        break
    fi
    if [ $i -eq 10 ]; then
        print_error "Backend health check failed"
        cleanup
        exit 1
    fi
    sleep 1
done

# Start frontend
print_status "Starting React frontend..."
cd piper_frontend
npm start &
FRONTEND_PID=$!
cd ..

# Wait a moment for frontend to start
sleep 3

# Check if frontend is running
if ! kill -0 $FRONTEND_PID 2>/dev/null; then
    print_error "Frontend failed to start"
    cleanup
    exit 1
fi

print_success "TTS Application started successfully!"
echo ""
print_status "🎤 Piper TTS API: http://localhost:8000"
print_status "🌐 React Frontend: http://localhost:3000"
print_status "📚 API Documentation: http://localhost:8000/docs"
echo ""
print_status "Available models:"
curl -s http://localhost:8000/models | python -c "
import sys, json
try:
    data = json.load(sys.stdin)
    for model in data['models']:
        current = ' (CURRENT)' if model['is_current'] else ''
        print(f\"  • {model['name']} ({model['type']}, {model['quality']} quality){current}\")
except:
    print('  • Unable to fetch model list')
"
echo ""
print_warning "Press Ctrl+C to stop both services"

# Keep script running and wait for processes
wait $BACKEND_PID $FRONTEND_PID
