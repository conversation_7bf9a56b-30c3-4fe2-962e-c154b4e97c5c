# 🎤 Voice Cloning & Timbre Transfer Guide

## 🎯 Overview

The Piper TTS API now supports advanced voice cloning and timbre transfer capabilities through a unified workflow:

1. **Base TTS Generation**: Your input text is first converted to speech using Piper TTS
2. **Voice Enhancement**: The generated audio is then enhanced with voice cloning or timbre transfer
3. **Final Output**: You get audio that sounds like the reference voice speaking your text

## 🔄 Workflow

```
Input Text → Piper TTS → Voice Cloning/Timbre Transfer → Final Audio
    ↓             ↓                    ↓                      ↓
"Hello world" → Base Audio → Apply Reference Voice → Reference Voice Speaking Text
```

## 🎛️ Available Models

### Voice Cloning Models
- **OpenVoice v2**: Instant voice cloning with good quality
- **SeedVC**: Advanced timbre transfer with prosody preservation
- **VALL-E X**: Research-grade voice cloning (placeholder)
- **F5-TTS**: Fast and high-quality voice cloning (install required)
- **XTTS v2**: Multilingual voice cloning (install required)
- **Bark**: Expressive voice cloning with emotions (install required)
- **Tortoise TTS**: Ultra high-quality voice cloning (install required)

### Timbre Transfer Models
- **OpenVoice v2**: Real-time timbre transfer
- **SeedVC**: Advanced timbre transfer with prosody control
- **XTTS v2**: Multilingual timbre transfer (install required)

## 🚀 Quick Start

### 1. Check Available Features
```bash
curl http://localhost:8000/features
```

### 2. Load a TTS Model
```bash
curl -X POST http://localhost:8000/models/finetuned_epoch_2899/load
```

### 3. Generate Audio with Voice Cloning
```bash
curl -X POST http://localhost:8000/generate \
  -H "Content-Type: application/json" \
  -d '{
    "text": "Hello, this is a test of voice cloning",
    "use_voice_cloning": true,
    "reference_audio_base64": "UklGRi4AAABXQVZFZm10...",
    "cloning_model": "openvoice_v2",
    "cloning_strength": 1.0
  }' \
  --output cloned_voice.wav
```

### 4. Generate Audio with Timbre Transfer
```bash
curl -X POST http://localhost:8000/generate \
  -H "Content-Type: application/json" \
  -d '{
    "text": "Hello, this is a test of timbre transfer",
    "use_timbre_transfer": true,
    "reference_audio_base64": "UklGRi4AAABXQVZFZm10...",
    "cloning_model": "seed_vc",
    "timbre_strength": 0.8,
    "preserve_prosody": true
  }' \
  --output timbre_transfer.wav
```

## 🎚️ Parameters

### Voice Cloning Parameters
- `use_voice_cloning`: Enable voice cloning
- `reference_audio_base64`: Base64-encoded reference audio
- `cloning_model`: Model to use (openvoice_v2, seed_vc, etc.)
- `cloning_strength`: Strength of voice cloning (0.1-2.0)

### Timbre Transfer Parameters
- `use_timbre_transfer`: Enable timbre transfer
- `reference_audio_base64`: Base64-encoded reference audio
- `cloning_model`: Model to use for timbre transfer
- `timbre_strength`: Strength of timbre transfer (0.1-2.0)
- `preserve_prosody`: Preserve original timing and rhythm

### Base TTS Parameters
- `text`: Text to synthesize
- `speaker_id`: Speaker ID (0-17 for current model)
- `speech_rate`: Speech rate (0.5-3.0)
- `noise_scale`: Generator noise (0.0-1.0)
- `noise_w`: Phoneme width noise (0.0-1.0)
- `sentence_silence`: Silence after sentences (0.0-2.0)

## 🔧 Installation

### Install Voice Cloning Models
```bash
# Run the installation script
./piper_api/install_voice_cloning_models.sh

# Or install manually
pip install f5-tts TTS bark tortoise-tts
```

### Install with GPU Support
```bash
# For NVIDIA GPUs
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
```

## 🎨 Frontend Usage

The frontend automatically detects available voice cloning and timbre transfer capabilities:

1. **Voice Cloning Section**: Upload reference audio and select model
2. **Timbre Transfer Section**: Apply voice characteristics to Piper TTS
3. **Dynamic UI**: Features show/hide based on backend capabilities

## 🔍 Response Headers

The API provides helpful headers in responses:

- `X-Generation-Method`: Method used (regular_tts, voice_cloning, timbre_transfer)
- `X-Cloning-Model`: Voice cloning model used
- `X-Transfer-Model`: Timbre transfer model used
- `X-Base-TTS-Size`: Size of base TTS audio in bytes
- `X-Fallback-Reason`: Reason if fallback to regular TTS occurred

## 🚨 Error Handling

The system provides graceful fallbacks:

1. **Voice cloning fails** → Falls back to regular TTS
2. **Timbre transfer fails** → Returns base TTS audio
3. **Model not available** → Uses available alternative or regular TTS
4. **Invalid parameters** → Returns clear error messages

## 🎯 Best Practices

1. **Reference Audio Quality**: Use clear, noise-free reference audio (3-10 seconds)
2. **Model Selection**: Choose appropriate model for your use case
3. **Parameter Tuning**: Start with default values and adjust gradually
4. **Error Handling**: Always check response headers for generation method
5. **Performance**: Consider using GPU acceleration for better performance

## 📊 Model Comparison

| Model | Speed | Quality | Languages | Timbre Transfer |
|-------|-------|---------|-----------|-----------------|
| OpenVoice v2 | ⚡⚡⚡ | ⭐⭐⭐ | Multi | ✅ |
| SeedVC | ⚡⚡ | ⭐⭐⭐⭐ | Multi | ✅ |
| F5-TTS | ⚡⚡⚡ | ⭐⭐⭐⭐ | Multi | ❌ |
| XTTS v2 | ⚡ | ⭐⭐⭐⭐⭐ | Multi | ✅ |
| Bark | ⚡ | ⭐⭐⭐⭐ | Multi | ❌ |
| Tortoise | ⚡ | ⭐⭐⭐⭐⭐ | English | ❌ |

## 🔗 API Endpoints

- `GET /features` - Get available capabilities
- `POST /generate` - Unified audio generation
- `GET /models` - List available TTS models
- `POST /models/{model_id}/load` - Load TTS model
- `GET /speakers` - Get speaker information
- `GET /health` - Health check

## 🎉 Success!

Your TTS API now supports advanced voice cloning and timbre transfer! The system automatically:

- ✅ Generates base TTS audio first
- ✅ Applies voice cloning/timbre transfer as post-processing
- ✅ Provides graceful fallbacks
- ✅ Reports generation method in headers
- ✅ Supports multiple models and languages
